@props([
    'title' => '',
    'url' => '',
    'description' => '',
    'image' => '',
    'type' => 'ad', // ad, job, job_seeker
    'size' => 'normal' // small, normal, large
])

@php
    $currentUrl = $url ?: request()->url();
    $pageTitle = $title ?: (isset($ad) ? $ad->title : (isset($job) ? $job->job_title : 'منصة انشر'));
    $pageDescription = $description ?: (isset($ad) ? Str::limit($ad->description, 100) : (isset($job) ? Str::limit($job->job_description, 100) : 'منصتك الأولى للإعلانات المبوبة'));
    $pageImage = $image ?: (isset($ad) && $ad->images->first() ? $ad->images->first()->image_url : asset('images/logo.png'));
    
    // تنظيف النصوص للمشاركة
    $cleanTitle = urlencode($pageTitle);
    $cleanDescription = urlencode($pageDescription);
    $cleanUrl = urlencode($currentUrl);
    $cleanImage = urlencode($pageImage);
    
    // تحديد أحجام الأزرار
    $sizeClasses = [
        'small' => 'btn-sm',
        'normal' => '',
        'large' => 'btn-lg'
    ];
    $buttonSize = $sizeClasses[$size] ?? '';
@endphp

<div class="share-button-container">
    <style>
        .share-button-container {
            position: relative;
        }
        
        .share-main-button {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
            border: none;
            border-radius: 50rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
            cursor: pointer;
        }
        
        .share-main-button:hover {
            background: linear-gradient(135deg, #0056b3 0%, #520dc2 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        }
        
        .share-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            padding: 1rem;
            min-width: 280px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .share-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .share-option {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 0.5rem;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
            border: 1px solid transparent;
        }
        
        .share-option:hover {
            text-decoration: none;
            color: white;
            transform: translateX(-5px);
        }
        
        .share-option.facebook:hover {
            background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
            border-color: #1877f2;
        }
        
        .share-option.twitter:hover {
            background: linear-gradient(135deg, #1da1f2 0%, #42a5f5 100%);
            border-color: #1da1f2;
        }
        
        .share-option.linkedin:hover {
            background: linear-gradient(135deg, #0077b5 0%, #42a5f5 100%);
            border-color: #0077b5;
        }
        
        .share-option.whatsapp:hover {
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            border-color: #25d366;
        }
        
        .share-option.telegram:hover {
            background: linear-gradient(135deg, #0088cc 0%, #42a5f5 100%);
            border-color: #0088cc;
        }
        
        .share-option.copy:hover {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border-color: #6c757d;
        }
        
        .share-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 0.75rem;
            font-size: 1.2rem;
        }
        
        .facebook .share-icon { background: #1877f2; color: white; }
        .twitter .share-icon { background: #1da1f2; color: white; }
        .linkedin .share-icon { background: #0077b5; color: white; }
        .whatsapp .share-icon { background: #25d366; color: white; }
        .telegram .share-icon { background: #0088cc; color: white; }
        .copy .share-icon { background: #6c757d; color: white; }
        
        .share-text {
            flex: 1;
        }
        
        .share-label {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .share-description {
            font-size: 0.875rem;
            color: #6c757d;
            margin: 0;
        }
        
        .copy-feedback {
            position: absolute;
            top: -40px;
            right: 0;
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
            z-index: 1001;
        }
        
        .copy-feedback.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        @media (max-width: 768px) {
            .share-dropdown {
                right: auto;
                left: 0;
                min-width: 250px;
            }
        }
    </style>

    <!-- زر المشاركة الرئيسي -->
    <button type="button" class="share-main-button {{ $buttonSize }}" onclick="toggleShareDropdown(this)">
        <i class="fas fa-share-alt"></i>
        <span>مشاركة</span>
        <i class="fas fa-chevron-down ms-1" style="font-size: 0.8rem;"></i>
    </button>

    <!-- قائمة خيارات المشاركة -->
    <div class="share-dropdown">
        <div class="copy-feedback" id="copyFeedback">تم نسخ الرابط!</div>
        
        <!-- فيسبوك -->
        <a href="https://www.facebook.com/sharer/sharer.php?u={{ $cleanUrl }}&quote={{ $cleanTitle }}" 
           target="_blank" 
           class="share-option facebook"
           onclick="trackShare('facebook')">
            <div class="share-icon">
                <i class="fab fa-facebook-f"></i>
            </div>
            <div class="share-text">
                <div class="share-label">فيسبوك</div>
                <p class="share-description">شارك مع أصدقائك على فيسبوك</p>
            </div>
        </a>

        <!-- تويتر -->
        <a href="https://twitter.com/intent/tweet?text={{ $cleanTitle }}&url={{ $cleanUrl }}&hashtags=انشر,وظائف,إعلانات" 
           target="_blank" 
           class="share-option twitter"
           onclick="trackShare('twitter')">
            <div class="share-icon">
                <i class="fab fa-twitter"></i>
            </div>
            <div class="share-text">
                <div class="share-label">تويتر</div>
                <p class="share-description">غرد عن هذا المحتوى</p>
            </div>
        </a>

        <!-- لينكد إن -->
        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ $cleanUrl }}&title={{ $cleanTitle }}&summary={{ $cleanDescription }}" 
           target="_blank" 
           class="share-option linkedin"
           onclick="trackShare('linkedin')">
            <div class="share-icon">
                <i class="fab fa-linkedin-in"></i>
            </div>
            <div class="share-text">
                <div class="share-label">لينكد إن</div>
                <p class="share-description">شارك مع شبكتك المهنية</p>
            </div>
        </a>

        <!-- واتساب -->
        <a href="https://wa.me/?text={{ $cleanTitle }}%20{{ $cleanUrl }}" 
           target="_blank" 
           class="share-option whatsapp"
           onclick="trackShare('whatsapp')">
            <div class="share-icon">
                <i class="fab fa-whatsapp"></i>
            </div>
            <div class="share-text">
                <div class="share-label">واتساب</div>
                <p class="share-description">أرسل عبر واتساب</p>
            </div>
        </a>

        <!-- تيليجرام -->
        <a href="https://t.me/share/url?url={{ $cleanUrl }}&text={{ $cleanTitle }}" 
           target="_blank" 
           class="share-option telegram"
           onclick="trackShare('telegram')">
            <div class="share-icon">
                <i class="fab fa-telegram-plane"></i>
            </div>
            <div class="share-text">
                <div class="share-label">تيليجرام</div>
                <p class="share-description">شارك على تيليجرام</p>
            </div>
        </a>

        <!-- نسخ الرابط -->
        <a href="#" 
           class="share-option copy"
           onclick="copyToClipboard('{{ $currentUrl }}', event)">
            <div class="share-icon">
                <i class="fas fa-copy"></i>
            </div>
            <div class="share-text">
                <div class="share-label">نسخ الرابط</div>
                <p class="share-description">انسخ الرابط للمشاركة</p>
            </div>
        </a>
    </div>
</div>

<script>
// إدارة قائمة المشاركة
function toggleShareDropdown(button) {
    const dropdown = button.nextElementSibling;
    const isVisible = dropdown.classList.contains('show');
    
    // إغلاق جميع القوائم المفتوحة
    document.querySelectorAll('.share-dropdown.show').forEach(d => {
        d.classList.remove('show');
    });
    
    // فتح/إغلاق القائمة الحالية
    if (!isVisible) {
        dropdown.classList.add('show');
    }
}

// نسخ الرابط
function copyToClipboard(url, event) {
    event.preventDefault();
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
            showCopyFeedback();
            trackShare('copy');
        });
    } else {
        // للمتصفحات القديمة
        const textArea = document.createElement('textarea');
        textArea.value = url;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showCopyFeedback();
        trackShare('copy');
    }
}

// إظهار رسالة النسخ
function showCopyFeedback() {
    const feedback = document.getElementById('copyFeedback');
    feedback.classList.add('show');
    setTimeout(() => {
        feedback.classList.remove('show');
    }, 2000);
}

// تتبع المشاركات (يمكن ربطه بـ Google Analytics)
function trackShare(platform) {
    console.log('مشاركة على:', platform);
    
    // يمكن إضافة تتبع Google Analytics هنا
    if (typeof gtag !== 'undefined') {
        gtag('event', 'share', {
            'method': platform,
            'content_type': '{{ $type }}',
            'item_id': '{{ request()->route("id") ?? "unknown" }}'
        });
    }
}

// إغلاق القائمة عند النقر خارجها
document.addEventListener('click', function(event) {
    if (!event.target.closest('.share-button-container')) {
        document.querySelectorAll('.share-dropdown.show').forEach(dropdown => {
            dropdown.classList.remove('show');
        });
    }
});

// إغلاق القائمة عند الضغط على Escape
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        document.querySelectorAll('.share-dropdown.show').forEach(dropdown => {
            dropdown.classList.remove('show');
        });
    }
});
</script>
