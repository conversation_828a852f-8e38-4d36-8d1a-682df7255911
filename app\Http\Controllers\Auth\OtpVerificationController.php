<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class OtpVerificationController extends Controller
{
    /**
     * Display the OTP verification view.
     */
    public function create(Request $request): View
    {
        return view('auth.verify-otp', ['email' => $request->email]);
    }

    /**
     * Verify the OTP code.
     */
    public function verify(Request $request)
    {
        $request->validate([
            'email' => ['required', 'string', 'email'],
            'otp' => ['required', 'string', 'min:6', 'max:6'],
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return back()->withErrors(['email' => 'لم يتم العثور على المستخدم.']);
        }

        if ($user->otp !== $request->otp) {
            return back()->withErrors(['otp' => 'رمز التحقق غير صحيح.']);
        }

        if (Carbon::now()->isAfter($user->otp_expires_at)) {
            return back()->withErrors(['otp' => 'انتهت صلاحية رمز التحقق. يرجى طلب رمز جديد.']);
        }

        // Mark user as verified
        $user->is_verified = true;
        $user->otp = null;
        $user->otp_expires_at = null;
        $user->email_verified_at = Carbon::now();
        $user->save();

        // Log the user in
        Auth::login($user);

        return redirect(RouteServiceProvider::HOME);
    }

    /**
     * Resend OTP code.
     */
    public function resend(Request $request)
    {
        $request->validate([
            'email' => ['required', 'string', 'email'],
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return back()->withErrors(['email' => 'لم يتم العثور على المستخدم.']);
        }

        // Generate new OTP
        $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
        $user->otp = $otp;
        $user->otp_expires_at = Carbon::now()->addMinutes(10);
        $user->save();

        // Send OTP notification
        $user->notify(new \App\Notifications\OtpVerificationNotification($otp));

        return back()->with('status', 'تم إرسال رمز تحقق جديد إلى بريدك الإلكتروني.');
    }
}
